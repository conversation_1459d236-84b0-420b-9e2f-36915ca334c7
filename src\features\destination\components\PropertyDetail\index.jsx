import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useEffect, useState, useCallback } from "react";
import {
  FiLayers,
  FiDollarSign,
  FiMapPin,
  FiCheckCircle,
  FiDownload,
  FiHome,
  FiCalendar,
  FiStar,
  FiInfo,
  FiImage,
  FiX,
  FiChevronLeft,
  FiChevronRight,
} from "react-icons/fi";
import axios from "axios";
import FooterWithEmail from "../../../../layout/FooterWithEmail";

function PropertyDetail() {
  const { id } = useParams();
  const [property, setProperty] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const fetchProperty = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `http://localhost:3001/api/projects/${id}`
        );
        setProperty(response.data);
      } catch (error) {
        console.error("Failed to fetch property:", error);
        setProperty(null);
      } finally {
        setLoading(false);
      }
    };
    fetchProperty();
  }, [id]);

  // Gallery helper functions
  const openModal = (index) => {
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const nextImage = useCallback(() => {
    if (property?.gallery) {
      setSelectedImageIndex((prev) =>
        prev === property.gallery.length - 1 ? 0 : prev + 1
      );
    }
  }, [property?.gallery]);

  const prevImage = useCallback(() => {
    if (property?.gallery) {
      setSelectedImageIndex((prev) =>
        prev === 0 ? property.gallery.length - 1 : prev - 1
      );
    }
  }, [property?.gallery]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (isModalOpen) {
        if (e.key === "Escape") closeModal();
        if (e.key === "ArrowRight") nextImage();
        if (e.key === "ArrowLeft") prevImage();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isModalOpen, nextImage, prevImage]);

  if (loading)
    return (
      <div className="min-h-screen bg-[#EBE6E2] flex items-center justify-center pt-55">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-8 border-primary border-t-transparent rounded-full animate-spin mb-4" />
          <div className="text-2xl font-semibold text-primary animate-pulse">
            Loading property details...
          </div>
        </div>
      </div>
    );

  if (!property)
    return (
      <div className="min-h-screen bg-[#EBE6E2] p-10 pt-55">
        <div className="max-w-5xl mx-auto">
          <Link
            to="/destinations"
            className="text-primary hover:underline mb-8 inline-block"
          >
            ← Back to properties
          </Link>
          <div className="text-3xl">Property not found</div>
        </div>
      </div>
    );

  const mainUnit = property.unit_types?.[0];
  const startingPrice = mainUnit
    ? `AED ${mainUnit.starting_price.toLocaleString()}`
    : "N/A";

  return (
    <div className="min-h-screen bg-[#EBE6E2] pt-55">
      {/* Hero Section */}
      <div className="w-full h-[65vh] relative flex items-end mb-10">
        <img
          src={property.projectHero || property.mainImage}
          alt={property.title}
          className="w-full h-full object-cover absolute inset-0 z-0"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10" />
        <div className="relative z-20 px-8 pb-10 max-w-4xl mx-auto w-full">
          <h1 className="text-5xl font-extrabold text-white mb-2 drop-shadow-lg">
            {property.title}
          </h1>
          {property.subtitle && (
            <h2 className="text-2xl md:text-3xl text-primary font-semibold italic mb-2 drop-shadow-lg">
              {property.subtitle}
            </h2>
          )}
          {property.summary && (
            <p className="text-xl md:text-2xl text-white font-medium drop-shadow mb-2 max-w-2xl">
              {property.summary}
            </p>
          )}
        </div>
      </div>

      <div className="mx-auto px-4 md:px-8 py-10 max-w-7xl">
        <Link
          to="/destinations"
          className="text-primary hover:underline mb-8 inline-block text-lg font-medium"
        >
          ← Back to properties
        </Link>

        {/* 1. Enhanced Gallery Section */}
        {property.gallery && property.gallery.length > 0 && (
          <section className="mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Enhanced Gallery Section - Left */}
              <div className="lg:order-1">
                <div className="bg-white rounded-2xl shadow-xl p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <FiImage className="text-3xl text-primary" />
                    <h3 className="text-2xl font-bold text-primary">
                      Property Gallery ({property.gallery.length} images)
                    </h3>
                  </div>

                  {/* Main Gallery Image */}
                  <div className="relative mb-4">
                    <img
                      src={property.gallery[selectedImageIndex]}
                      alt={`Gallery ${selectedImageIndex + 1}`}
                      className="w-full h-[400px] object-cover rounded-xl shadow-lg cursor-pointer transition-transform duration-300 hover:scale-105"
                      onClick={() => openModal(selectedImageIndex)}
                    />
                    <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                      {selectedImageIndex + 1} / {property.gallery.length}
                    </div>
                  </div>

                  {/* Thumbnail Gallery */}
                  <div className="grid grid-cols-4 gap-2">
                    {property.gallery.slice(0, 8).map((image, index) => (
                      <div
                        key={index}
                        className={`relative cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                          selectedImageIndex === index
                            ? "ring-3 ring-primary shadow-lg"
                            : "hover:ring-2 hover:ring-primary/50"
                        }`}
                        onClick={() => setSelectedImageIndex(index)}
                      >
                        <img
                          src={image}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-16 object-cover"
                        />
                        {selectedImageIndex === index && (
                          <div className="absolute inset-0 bg-primary/20" />
                        )}
                      </div>
                    ))}
                    {property.gallery.length > 8 && (
                      <div
                        className="relative cursor-pointer rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors"
                        onClick={() => openModal(8)}
                      >
                        <span className="text-sm font-semibold text-gray-600">
                          +{property.gallery.length - 8}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Main Info & Description Section - Right */}
              <div className="lg:order-2">
                <section className="bg-white rounded-2xl shadow-xl p-10 h-full flex flex-col justify-between">
                  <div>
                    <div className="flex items-center gap-3 mb-4">
                      <FiHome className="text-3xl text-primary" />
                      <h1 className="text-4xl font-extrabold text-gray-900">
                        {property.title}
                      </h1>
                    </div>

                    {property.subtitle && (
                      <div className="flex items-center gap-3 mb-6">
                        <FiStar className="text-2xl text-primary" />
                        <div className="text-2xl text-primary font-semibold italic">
                          {property.subtitle}
                        </div>
                      </div>
                    )}

                    {property.summary && (
                      <div className="flex items-start gap-3 mb-6">
                        <FiInfo className="text-xl text-primary mt-1" />
                        <div className="text-lg text-gray-700 leading-relaxed">
                          {property.summary}
                        </div>
                      </div>
                    )}

                    {property.desc && (
                      <div className="flex items-start gap-3 mb-8">
                        <FiInfo className="text-xl text-primary mt-1" />
                        <p className="text-lg text-gray-700 leading-relaxed">
                          {property.desc}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-6">
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
                        <FiHome className="text-xl" />
                        {property.type}
                      </div>
                      <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
                        <FiCalendar className="text-xl" />
                        Handover: {property.handover}
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </section>
        )}

        {/* 2. Second Row: Unit Types (Left) + Gallery (Right) */}
        {property.unit_types && property.unit_types.length > 0 && (
          <section className="mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Unit Types & Prices Section - Left */}
              <div className="lg:order-1">
                <section className="bg-white p-10 rounded-2xl shadow-md h-full flex flex-col">
                  <div className="flex items-center gap-3 mb-6">
                    <FiLayers className="text-3xl text-primary" />
                    <h3 className="text-3xl font-bold text-primary border-b-4 border-primary pb-2">
                      Unit Types & Prices
                    </h3>
                  </div>

                  <div className="flex-1 overflow-x-auto">
                    <table className="min-w-full text-lg">
                      <thead>
                        <tr className="text-left border-b-2 border-primary">
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Type
                          </th>
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Size (sqft)
                          </th>
                          <th className="py-4 pr-8 text-xl font-bold text-primary">
                            Starting Price
                          </th>
                        </tr>
                      </thead>
                      <tbody className="space-y-2">
                        {property.unit_types.map((unit, index) => (
                          <tr
                            key={unit._id}
                            className={`border-b last:border-b-0 hover:bg-gray-50 transition-colors ${
                              index % 2 === 0 ? "bg-gray-50/50" : ""
                            }`}
                          >
                            <td className="py-4 pr-8 font-semibold text-lg">
                              {unit.type}
                            </td>
                            <td className="py-4 pr-8 text-lg">
                              {unit.size_range_sqft}
                            </td>
                            <td className="py-4 pr-8 text-primary font-bold text-xl">
                              AED {unit.starting_price.toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="mt-6 pt-6 border-t-2 border-primary/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <FiDollarSign className="text-2xl text-primary" />
                        <span className="text-lg font-semibold text-gray-700">
                          Total Unit Types: {property.unit_types.length}
                        </span>
                      </div>
                      <div className="text-2xl font-bold text-primary">
                        Starting from {startingPrice}
                      </div>
                    </div>
                  </div>
                </section>
              </div>

              {/* Additional Property Features - Right */}
              <div className="lg:order-2">
                <div className="bg-white rounded-2xl shadow-xl p-8 h-full">
                  <div className="flex items-center gap-3 mb-6">
                    <FiCheckCircle className="text-3xl text-primary" />
                    <h3 className="text-2xl font-bold text-primary">
                      Property Features
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                      <FiHome className="text-xl text-primary" />
                      <span className="font-semibold">Property Type:</span>
                      <span className="text-primary font-bold">
                        {property.type}
                      </span>
                    </div>

                    <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                      <FiCalendar className="text-xl text-primary" />
                      <span className="font-semibold">Handover:</span>
                      <span className="text-primary font-bold">
                        {property.handover}
                      </span>
                    </div>

                    {property.location && (
                      <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                        <FiMapPin className="text-xl text-primary" />
                        <span className="font-semibold">Location:</span>
                        <span className="text-primary font-bold">
                          {property.location.area}, {property.location.city}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                      <FiLayers className="text-xl text-primary" />
                      <span className="font-semibold">Unit Types:</span>
                      <span className="text-primary font-bold">
                        {property.unit_types?.length || 0} types available
                      </span>
                    </div>

                    {property.brochure && (
                      <a
                        href={property.brochure}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-4 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-semibold"
                      >
                        <FiDownload className="text-xl" />
                        Download Property Brochure
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* 3. Full Width: Nearby & Location Section */}
        <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
          <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
            <FiMapPin className="text-primary" /> Location & Nearby
          </h3>
          <div className="mb-2 text-lg">
            <span className="font-semibold">Area:</span>{" "}
            {property.location?.area}
          </div>
          <div className="mb-2 text-lg">
            <span className="font-semibold">City:</span>{" "}
            {property.location?.city}
          </div>
          {property.location?.nearby && property.location.nearby.length > 0 && (
            <div className="mb-2">
              <div className="font-semibold mb-1">Nearby:</div>
              <div className="flex flex-wrap gap-2">
                {property.location.nearby.map((n, i) => (
                  <span
                    key={i}
                    className="bg-primary/10 text-primary px-3 py-1 rounded-full text-base font-medium"
                  >
                    {n}
                  </span>
                ))}
              </div>
            </div>
          )}
        </section>

        {/* 5. Amenities Section */}
        <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
          <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
            <FiCheckCircle className="text-primary" /> Amenities
          </h3>
          <ul className="list-disc pl-6 space-y-2 text-lg">
            {property.amenities?.map((a, i) => (
              <li key={i}>{a}</li>
            ))}
          </ul>
        </section>

        {/* 6. Payment Plan Section */}
        {property.payment_plan && (
          <section className="bg-white p-8 rounded-2xl shadow-md mb-12">
            <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
              <FiDollarSign className="text-primary" /> Payment Plan
            </h3>
            <div className="mb-2 text-lg font-semibold">
              {property.payment_plan.summary}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-4">
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  Down Payment
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.down_payment_percent}%
                </span>
              </div>
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  During Construction
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.during_construction_percent}%
                </span>
              </div>
              <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
                <span className="text-primary font-bold text-lg mb-1">
                  Post-Handover
                </span>
                <span className="text-2xl font-extrabold">
                  {property.payment_plan.details.post_handover_percent}%
                </span>
              </div>
            </div>
            {property.payment_plan.details.monthly_installments && (
              <div className="mt-4 text-base text-gray-700">
                Monthly Installments for{" "}
                <span className="font-semibold">
                  {property.payment_plan.details.total_months}
                </span>{" "}
                months
              </div>
            )}
          </section>
        )}

        {/* 7. CTA Section */}
        {/* {property.call_to_action && (
          <section className="bg-primary rounded-2xl p-10 flex flex-col items-center gap-4 mt-4 shadow-xl">
            <div className="text-2xl font-bold text-white mb-2">
              Consultant: {property.call_to_action.consultant}
            </div>
            <div className="flex flex-wrap gap-6 justify-center">
              {property.call_to_action.actions.map((action) => (
                <a
                  key={action._id}
                  href={action.url}
                  target="_blank"
                  rel="noreferrer"
                  className="bg-white text-primary py-4 px-10 rounded-xl text-xl hover:bg-opacity-90 transition-all shadow-lg font-bold border-2 border-primary"
                >
                  {action.label}
                </a>
              ))}
            </div>
          </section>
        )} */}
      </div>

      {/* Gallery Modal */}
      {isModalOpen && property.gallery && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-6xl max-h-full w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
            >
              <FiX className="text-2xl" />
            </button>

            {/* Navigation Buttons */}
            {property.gallery.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
                >
                  <FiChevronLeft className="text-2xl" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
                >
                  <FiChevronRight className="text-2xl" />
                </button>
              </>
            )}

            {/* Main Image */}
            <img
              src={property.gallery[selectedImageIndex]}
              alt={`Gallery ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full">
              {selectedImageIndex + 1} / {property.gallery.length}
            </div>

            {/* Thumbnail Strip */}
            {property.gallery.length > 1 && (
              <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex gap-2 max-w-full overflow-x-auto px-4">
                {property.gallery.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden transition-all ${
                      selectedImageIndex === index
                        ? "ring-2 ring-white opacity-100"
                        : "opacity-60 hover:opacity-80"
                    }`}
                  >
                    <img
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* <Email/> */}
      <FooterWithEmail />
    </div>
  );
}

export default PropertyDetail;
