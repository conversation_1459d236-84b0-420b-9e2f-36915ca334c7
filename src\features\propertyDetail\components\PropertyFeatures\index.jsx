import PropTypes from "prop-types";
import {
  FiCheckCircle,
  FiHome,
  FiCalendar,
  FiMapPin,
  FiLayers,
  FiDownload,
} from "react-icons/fi";

function PropertyFeatures({ property }) {
  if (!property) return null;

  return (
    <section className="mb-12">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full">
        <div className="flex items-center gap-3 mb-6">
          <FiCheckCircle className="text-3xl text-primary" />
          <h3 className="text-2xl font-bold text-primary">Property Features</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <FiHome className="text-xl text-primary" />
            <span className="font-semibold">Property Type:</span>
            <span className="text-primary font-bold">{property.type}</span>
          </div>

          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <FiCalendar className="text-xl text-primary" />
            <span className="font-semibold">Handover:</span>
            <span className="text-primary font-bold">{property.handover}</span>
          </div>

          {property.location && (
            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
              <FiMapPin className="text-xl text-primary" />
              <span className="font-semibold">Location:</span>
              <span className="text-primary font-bold">
                {property.location.area}, {property.location.city}
              </span>
            </div>
          )}

          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <FiLayers className="text-xl text-primary" />
            <span className="font-semibold">Unit Types:</span>
            <span className="text-primary font-bold">
              {property.unit_types?.length || 0} types available
            </span>
          </div>

          {property.brochure && (
            <a
              href={property.brochure}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-semibold"
            >
              <FiDownload className="text-xl" />
              Download Property Brochure
            </a>
          )}
        </div>
      </div>
    </section>
  );
}

PropertyFeatures.propTypes = {
  property: PropTypes.shape({
    type: PropTypes.string,
    handover: PropTypes.string,
    location: PropTypes.shape({
      area: PropTypes.string,
      city: PropTypes.string,
    }),
    unit_types: PropTypes.array,
    brochure: PropTypes.string,
  }).isRequired,
};

export default PropertyFeatures;
