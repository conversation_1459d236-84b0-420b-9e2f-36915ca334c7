import firstImg from "../../../../assets/images/LL_4.jpg";
import secondImg from "../../../../assets/images/LL_1.jpg";
import thirdImg from "../../../../assets/images/LL_2.jpg";
import fourthImg from "../../../../assets/images/LL_3.jpg";
import Subtitle from "../../../../components/Subtitle";

const companies = [
  "OMNIYAT",
  "EAGLE HILLS",
  "SAMANA",
  "EMAAR",
  "SOBHA",
  "IMTIAZ",
];

function LatestLaunches() {
  return (
    <section className="w-full mx-auto bg-secondary text-white py-20 lg:px-0 overflow-hidden">
      <div className="mx-auto px-6 md:px-50">
        <h2 className="md:col-span-8 text-5xl text-center mx-auto font-medium mb-10">
          Discover a{" "}
          <span className="text-primary italic font-semibold">
            Premium Selection
          </span>
          <br />
          of our properties and and Exclusive Deals
        </h2>
        <Subtitle text="Latest Launches" />
      </div>

      {/* Images Layout */}
      <div className="min-w-[150rem] overflow-clipped">
        <div className="-translate-x-96 xl:-translate-x-0 flex overflow-clipped justify-between items-center gap-4 lg:gap-6 xl:gap-10 px-0">
          <img
            src={firstImg}
            alt="Preview 1"
            className="flex-1 w-22 h-60 lg:w-[25rem] lg:h-[20rem] 2xl:w-[29.1rem] 2xl:h-[24.4rem]   object-cover rounded-lg"
          />
          <div className="flex-1 flex-col">
            <img
              src={secondImg}
              alt="Main Preview"
              className="h-96 2xl:h-[46.5rem] 2xl:w-[44.1rem] lg:h-[55rem] lg:w-[58rem] object-cover rounded-lg"
            />
            <div className=" mt-6">
              <p className="text-2xl md:text-5xl">Samana Barari Heights</p>
              <p className="text-white text-sm md:text-2xl">Majan, q3 2028</p>
            </div>
          </div>
          <img
            src={thirdImg}
            alt="Preview 2"
            className="flex-1 w-72 h-60 lg:w-[25rem] lg:h-[20rem] 2xl:w-[29.1rem] 2xl:h-[24.4rem]  object-cover rounded-lg"
          />
          <img
            src={fourthImg}
            alt="Wide Preview"
            className="flex-1 w-2xl h-[28rem] 2xl:w-[58.4rem] 2xl:h-[35.2rem] lg:w-[45rem] lg:h-[31rem] object-cover rounded-lg"
          />
        </div>
      </div>

      {/* C ompanies Row */}
      <div className="mt-20 w-full flex flex-wrap gap-8 lg:flex-nowrap justify-between items-center text-gray-400 gap-y-4">
        <p className="whitespace-nowrap text-2xl pl-55 ">
          Trusted by
          <span className="pl-2 font-semibold text-white">150+ companies</span>
          <br></br>
          from startups to enterprises:
        </p>
        {/* Scrolling Banner */}
        <ScrollingBanner companies={companies} speed={10} />
      </div>
    </section>
  );
}

export default LatestLaunches;

const ScrollingBanner = ({ companies, speed = 30 }) => {
  // const duplicatedCompanies = [...companies, ...companies, ...companies];

  return (
    <div className="banner-container font-sans">
      <div className="banner-content" style={{ "--duration": `${speed}s` }}>
        <div className="banner-items">
          {companies.map((company, index) => (
            <span key={`original-${index}`} className="banner-item">
              {company}
            </span>
          ))}
        </div>
        <div className="banner-items" aria-hidden="true">
          {companies.map((company, index) => (
            <span key={`duplicate-${index}`} className="banner-item">
              {company}
            </span>
          ))}
        </div>
      </div>

      <style jsx>{`
        .banner-container {
          width: 100%;
          overflow: hidden;
          padding: 2rem 0;
        }

        .banner-content {
          display: flex;
          animation: scroll var(--duration, 20s) linear infinite;
          width: max-content;
        }

        .banner-items {
          display: flex;
          gap: 5rem;
          white-space: nowrap;
        }

        .banner-item {
          letter-spacing: 0.3rem;
          color: white;
          font-weight: 600;
          font-size: 1rem;
          flex-shrink: 0;
        }

        @media (min-width: 1024px) {
          .banner-item {
            font-size: 1.875rem;
          }
        }

        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        /* Pause on hover for better UX */
        .banner-container:hover .banner-content {
          animation-play-state: paused;
        }
      `}</style>
    </div>
  );
};

// import React, { useEffect, useRef, useState } from "react";

// const ImageCarousel = () => {
//   const containerRef = useRef(null);
//   const [images] = useState([
//     {
//       id: 1,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Samana Barari Heights",
//       subtitle: "Majan, Q3 2028",
//     },
//     {
//       id: 2,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Azure Sky Tower",
//       subtitle: "Downtown, Q4 2027",
//     },
//     {
//       id: 3,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Crystal Residences",
//       subtitle: "Marina District, Q1 2029",
//     },
//     {
//       id: 4,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Platinum Heights",
//       subtitle: "Business Bay, Q2 2028",
//     },
//     {
//       id: 5,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Golden Vista",
//       subtitle: "Palm Jumeirah, Q3 2029",
//     },
//     {
//       id: 6,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Emerald Tower",
//       subtitle: "DIFC, Q4 2028",
//     },
//     {
//       id: 7,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Sapphire Complex",
//       subtitle: "JBR, Q1 2030",
//     },
//     {
//       id: 8,
//       src: "../../../../assets/images/LL_1.jpg",
//       title: "Diamond Plaza",
//       subtitle: "City Center, Q2 2029",
//     },
//   ]);

//   const [animatedImages, setAnimatedImages] = useState([]);

//   useEffect(() => {
//     const screenWidth = window.innerWidth;
//     const quarterWidth = screenWidth / 4;

//     // Initialize images with positions across the screen and beyond (starting from right for left scroll)
//     const initialImages = images.map((img, index) => {
//       const baseX = screenWidth + index * (quarterWidth * 0.8); // Start all off-screen to the right
//       return {
//         ...img,
//         x: baseX,
//         size: 120,
//         opacity: 0.6,
//         zIndex: 1,
//         showCaption: false,
//       };
//     });

//     setAnimatedImages(initialImages);
//   }, [images]);

//   useEffect(() => {
//     const screenWidth = window.innerWidth;
//     const quarterWidth = screenWidth / 4;
//     const gap = 40; // Add gap between images

//     // Initialize images with positions across the screen and beyond (starting from right for left scroll)
//     const initialImages = images.map((img, index) => {
//       const baseX = screenWidth + index * (quarterWidth * 0.8 + gap); // Add gap to spacing
//       return {
//         ...img,
//         x: baseX,
//         size: 120,
//         opacity: 0.6,
//         zIndex: 1,
//         showCaption: false,
//       };
//     });

//     setAnimatedImages(initialImages);
//   }, [images]);

//   useEffect(() => {
//     const animateImages = () => {
//       setAnimatedImages((prevImages) =>
//         prevImages.map((img) => {
//           const screenWidth = window.innerWidth;
//           const quarterWidth = screenWidth / 4;
//           const gap = 100; // Same gap value

//           // Move image to the left (changed from +1.5 to -1.5)
//           let newX = img.x - 1.5;

//           // Reset position if image goes too far left (cycle back to right)
//           if (newX < -200) {
//             newX = screenWidth + 200 + images.length * gap; // Account for gaps when resetting
//           }

//           // Calculate which quarter the image is in
//           let newSize = 120;
//           let newOpacity = 0.6;
//           let newZIndex = 1;
//           let showCaption = false;

//           // First quarter (left edge)
//           if (newX >= 0 && newX < quarterWidth) {
//             const progress = newX / quarterWidth;
//             newSize = 120 + 30 * progress; // 120px to 150px
//             newOpacity = 0.6 + 0.2 * progress;
//             newZIndex = 2;
//           }
//           // Second quarter (main showcase)
//           else if (newX >= quarterWidth && newX < quarterWidth * 2) {
//             const progress = (newX - quarterWidth) / quarterWidth;
//             newSize = 150 + 200 * progress; // 150px to 350px
//             newOpacity = 0.8 + 0.2 * progress;
//             newZIndex = 3;
//             showCaption = progress > 0.3; // Show caption when mostly transitioned
//           }
//           // Third quarter (transitioning down)
//           else if (newX >= quarterWidth * 2 && newX < quarterWidth * 3) {
//             const progress = (newX - quarterWidth * 2) / quarterWidth;
//             newSize = 350 - 200 * progress; // 350px to 150px
//             newOpacity = 1 - 0.2 * progress;
//             newZIndex = 2;
//             showCaption = progress < 0.9;
//           }
//           // Fourth quarter (smallest)
//           else if (newX >= quarterWidth * 3 && newX < quarterWidth * 4) {
//             const progress = (newX - quarterWidth * 3) / quarterWidth;
//             newSize = 150 - 30 * progress; // 150px to 120px
//             newOpacity = 0.8 - 0.2 * progress;
//             newZIndex = 1;
//           }

//           return {
//             ...img,
//             x: newX,
//             size: Math.max(newSize, 80), // Minimum size
//             opacity: Math.max(newOpacity, 0.3),
//             zIndex: newZIndex,
//             showCaption,
//           };
//         })
//       );
//     };

//     const interval = setInterval(animateImages, 16); // ~60fps
//     return () => clearInterval(interval);
//   }, []);

//   return (
//     <div
//       ref={containerRef}
//       className="relative w-full h-[500px] overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100"
//     >
//       {animatedImages.map((img) => (
//         <div
//           key={img.id}
//           className="absolute transition-all duration-200 ease-out"
//           style={{
//             left: `${img.x}px`,
//             top: "50%",
//             transform: "translateY(-50%)",
//             zIndex: img.zIndex,
//             opacity: img.opacity,
//           }}
//         >
//           <div className="relative">
//             <img
//               src={firstImg}
//               alt="Architectural showcase"
//               className="object-cover rounded-lg shadow-lg transition-all duration-300"
//               style={{
//                 width: `${img.size}px`,
//                 height: `${img.size}px`,
//               }}
//             />
//             {img.showCaption && img.title && (
//               <div className="absolute -bottom-16 left-0 text-left animate-fade-in">
//                 <p className="text-2xl font-semibold text-slate-800 mb-1">
//                   {img.title}
//                 </p>
//                 <p className="text-slate-500 text-sm">{img.subtitle}</p>
//               </div>
//             )}
//           </div>
//         </div>
//       ))}
//     </div>
//   );
// };
