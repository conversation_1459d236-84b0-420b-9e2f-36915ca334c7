const news = [
  {
    title: `Dubai1 Launches Middle East’s First Tokenized Real Estate
              Investment Platform`,
    desc: `Breaking the most prominent barrier to real estate Investing today
              is the The Dubai Land Department (DLD). The entity just launched
              its pilot projec`,
    date: `10.02.2024`,
    duration: `4 min read`,
  },
  {
    title: `Dubai2 Launches Middle East’s First Tokenized Real Estate
              Investment Platform`,
    desc: `Breaking the most prominent barrier to real estate Investing today
              is the The Dubai Land Department (DLD). The entity just launched
              its pilot projec`,
    date: `10.02.2024`,
    duration: `4 min read`,
  },
  {
    title: `Dubai3 Launches Middle East’s First Tokenized Real Estate
              Investment Platform`,
    desc: `Breaking the most prominent barrier to real estate Investing today
              is the The Dubai Land Department (DLD). The entity just launched
              its pilot projec`,
    date: `10.02.2024`,
    duration: `4 min read`,
  },
];

function News() {
  return (
    <section className="pb-20 pt-14 bg-secondary text-white">
      <div className="mx-auto px-6 md:px-30">
        <h2 className="text-6xl font-normal pl-25">
          Latest real estate news, tips,
          <br />
          <span className="text-primary italic">and market updates</span>
        </h2>
        <ul className="mt-14 flex flex-wrap gap-8 justify-between">
          {news.map(({ title, date, desc, duration }) => (
            <li
              key={title}
              className="border border-white rounded-3xl px-6 py-8 col-span-1 max-w-[30rem] m-auto"
            >
              <h3 className="text-xl">{title}</h3>
              <p className="mt-6 text-gray text-base font-sans">{desc}</p>
              <div className="mt-6">
                <span className="bg-primary rounded-4xl py-1 px-4 mr-4 text-base">
                  {duration}
                </span>
                <span className="bg-primary rounded-4xl py-1 px-4 mr-4 text-base">
                  {date}
                </span>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
}

export default News;
