const CarouselController = ({
  totalItems,
  activeIndex,
  onIndexChange,
  itemsPerIndex,
}) => {
  const totalIndices = Math.ceil(totalItems / itemsPerIndex);

  return (
    <div className="flex justify-between gap-8 items-center">
      <p className="font-sans text-lg text-gray">
        <span className="text-primary">0{activeIndex + 1} </span>/ 0
        {totalIndices}
      </p>

      <div className="flex-1 h-0.5 py-0.5 relative bg-[#D3D1D0] shadow-inner">
        {/* Background Track */}
        <div className="flex">
          {Array.from({ length: totalIndices }).map((_, index) => (
            <button
              key={index}
              onClick={() => onIndexChange(index)}
              className="absolute -top-[.001rem] z-10 h-1 hover:bg-white/20"
              style={{
                left: `calc((100%/${totalIndices})*${index})`,
                width: `calc(100%/${totalIndices})`,
              }}
            />
          ))}
        </div>

        {/* Sliding Indicator */}
        <div
          className="absolute -top-[.001rem] h-1 bg-primary shadow-lg transition-all duration-300"
          style={{
            transform: `translateX(calc(((100%/${totalIndices})*${activeIndex})*${totalIndices}))`,
            width: `calc(100%/${totalIndices})`,
          }}
        />
      </div>

      <div className="text-primary">
        <span
          className="font-extrabold ml-3 cursor-pointer"
          onClick={() =>
            onIndexChange((activeIndex - 1 + totalIndices) % totalIndices)
          }
        >
          ←
        </span>
        <span
          className="font-extrabold ml-3 cursor-pointer"
          onClick={() => onIndexChange((activeIndex + 1) % totalIndices)}
        >
          →
        </span>
      </div>
    </div>
  );
};

export default CarouselController;
