import { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import { FiImage, FiX, FiChevronLeft, FiChevronRight } from "react-icons/fi";

function Gallery({ property }) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Gallery helper functions
  const openModal = (index) => {
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const nextImage = useCallback(() => {
    if (property?.gallery) {
      setSelectedImageIndex((prev) =>
        prev === property.gallery.length - 1 ? 0 : prev + 1
      );
    }
  }, [property?.gallery]);

  const prevImage = useCallback(() => {
    if (property?.gallery) {
      setSelectedImageIndex((prev) =>
        prev === 0 ? property.gallery.length - 1 : prev - 1
      );
    }
  }, [property?.gallery]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (isModalOpen) {
        if (e.key === "Escape") closeModal();
        if (e.key === "ArrowRight") nextImage();
        if (e.key === "ArrowLeft") prevImage();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isModalOpen, nextImage, prevImage]);

  if (!property?.gallery || property.gallery.length === 0) {
    return null;
  }

  return (
    <>
      <section className="mb-12">
        <div className="bg-white rounded-2xl shadow-xl p-6">
          <div className="flex items-center gap-3 mb-6">
            <FiImage className="text-3xl text-primary" />
            <h3 className="text-2xl font-bold text-primary">
              Property Gallery ({property.gallery.length} images)
            </h3>
          </div>

          {/* Main Gallery Image */}
          <div className="relative mb-4">
            <img
              src={property.gallery[selectedImageIndex]}
              alt={`Gallery ${selectedImageIndex + 1}`}
              className="w-full h-[400px] object-cover rounded-xl shadow-lg cursor-pointer transition-transform duration-300 hover:scale-105"
              onClick={() => openModal(selectedImageIndex)}
            />
            <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} / {property.gallery.length}
            </div>
          </div>

          {/* Thumbnail Gallery */}
          <div className="grid grid-cols-4 gap-2">
            {property.gallery.slice(0, 8).map((image, index) => (
              <div
                key={index}
                className={`relative cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                  selectedImageIndex === index
                    ? "ring-3 ring-primary shadow-lg"
                    : "hover:ring-2 hover:ring-primary/50"
                }`}
                onClick={() => setSelectedImageIndex(index)}
              >
                <img
                  src={image}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-16 object-cover"
                />
                {selectedImageIndex === index && (
                  <div className="absolute inset-0 bg-primary/20" />
                )}
              </div>
            ))}
            {property.gallery.length > 8 && (
              <div
                className="relative cursor-pointer rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors"
                onClick={() => openModal(8)}
              >
                <span className="text-sm font-semibold text-gray-600">
                  +{property.gallery.length - 8}
                </span>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Gallery Modal */}
      {isModalOpen && property.gallery && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-6xl max-h-full w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
            >
              <FiX className="text-2xl" />
            </button>

            {/* Navigation Buttons */}
            {property.gallery.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
                >
                  <FiChevronLeft className="text-2xl" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors"
                >
                  <FiChevronRight className="text-2xl" />
                </button>
              </>
            )}

            {/* Main Image */}
            <img
              src={property.gallery[selectedImageIndex]}
              alt={`Gallery ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full">
              {selectedImageIndex + 1} / {property.gallery.length}
            </div>

            {/* Thumbnail Strip */}
            {property.gallery.length > 1 && (
              <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex gap-2 max-w-full overflow-x-auto px-4">
                {property.gallery.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden transition-all ${
                      selectedImageIndex === index
                        ? "ring-2 ring-white opacity-100"
                        : "opacity-60 hover:opacity-80"
                    }`}
                  >
                    <img
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}

Gallery.propTypes = {
  property: PropTypes.shape({
    gallery: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};

export default Gallery;
