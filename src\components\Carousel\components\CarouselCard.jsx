const CarouselCard = ({ data, isActive, onClick, itemsPerIndex }) => {
  return (
    <div
      onClick={onClick}
      className={`
        relative overflow-hidden rounded-2xl cursor-pointer transition-all duration-500 ease-out
        ${
          isActive
            ? "transform scale-105 shadow-2xl ring-4 ring-white ring-opacity-60"
            : "transform scale-100 shadow-lg hover:shadow-xl hover:scale-102"
        }
      `}
    >
      {/* Background Gradient */}
      <div
        className={`absolute inset-0 bg-gradient-to-br ${data.color} opacity-90`}
      />

      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center mix-blend-overlay"
        style={{ backgroundImage: `url(${data.image})` }}
      />

      {/* Content */}
      <div className="relative z-10 p-8 text-white min-h-[300px] flex flex-col justify-between">
        <div>
          <h3 className="text-2xl font-bold mb-4 leading-tight">
            {data.title}
          </h3>
          <p className="text-white/90 leading-relaxed">{data.description}</p>
        </div>

        {/* Active Indicator */}
        <div className="flex items-center justify-between mt-6">
          <div
            className={`
            h-1 rounded-full transition-all duration-300
            ${isActive ? "w-12 bg-white" : "w-8 bg-white/60"}
          `}
          />
          <span className="text-sm font-medium opacity-80">0{data.id}</span>
        </div>
      </div>
    </div>
  );
};

export default CarouselCard;
