/* Add this new class for destinations page */
.header {
  width: 100%;
  background: transparent;
  position: absolute; /* Use absolute instead of fixed */
  top: 1.8rem; /* Position at the very top */
  left: 0;
  z-index: 10;
  transition: all 0.8s ease;
}

.headerScrolled {
  top: 0;
}

.headerScrolled {
  top: 0;
  background: rgba(235, 230, 226, 0.75);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 4px 20px rgba(18, 37, 63, 0.08);
  border-bottom: 1px solid rgba(235, 230, 226, 0.3);
}
.headerScrolled {
  border-image: none;
  border: none;
}
.inner {
  max-width: clamp(32rem, 80vw, 150.7rem);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
}
.logo {
  flex-shrink: 0;
}

.logo img {
  height: 13.4rem;
  width: 13.4rem;
}

.nav_home {
  flex: 1;
  max-width: 95.8rem;
  margin: 0 2rem;
  padding: 0 1rem;
  border-radius: 7rem;
  background: linear-gradient(
    109.06deg,
    var(--nav-gradient-start) 31.6%,
    var(--nav-gradient-end) 113.76%
  );
  border-image-source: linear-gradient(
    90deg,
    var(--border-gradient-start) 10.67%,
    var(--border-gradient-end) 132.45%
  );
  border-image-slice: 1;
  backdrop-filter: blur(4.152rem);
  -webkit-backdrop-filter: blur(4.152rem);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
}

.nav {
  flex: 1;
  max-width: 95.8rem;
  margin: 0 2rem;
  padding: 0 1rem;
  border-radius: 7rem;
  background: transparent; /* No background */
  border: none; /* Remove border */
  border-image: none; /* Remove border image */
  backdrop-filter: none; /* Remove backdrop filter */
  -webkit-backdrop-filter: none; /* Remove webkit backdrop filter */
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
}

.cta {
  flex-shrink: 0;
  font-family: "Outfit", sans-serif;
  font-weight: 600;
  font-size: 2rem;
  line-height: 120%;
  text-transform: uppercase;
  text-decoration: underline;
  text-decoration-thickness: 0.1rem;
  color: var(--nav-link-active-color);
  background: none;
  border: none;
  cursor: pointer;
}

.navContainer {
  display: flex;
  height: 8rem;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}

.linkList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.socialList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--nav-link-active-color);
  padding: 1rem;
  border-radius: 50%;
}

.socialIcon {
  display: block;
  width: 1.35rem;
  height: 1.3rem;
}

/* Navigation Link Styles */
.link {
  font-family: "Outfit", sans-serif;
  font-weight: 400;
  font-size: 2rem;
  line-height: 120%;
  text-transform: uppercase;
  color: inherit;
  text-decoration: none;
  padding: 0.8rem 1.6rem;
}

.activeLink {
  font-family: "Outfit", sans-serif;
  font-weight: 400;
  font-size: 2rem;
  line-height: 120%;
  text-transform: uppercase;
  color: var(--nav-link-active-color);
  text-decoration: underline;
  text-decoration-thickness: 0.1rem;
  padding: 0.8rem 1.6rem;
}

/* Mobile menu toggle button */
.mobileMenuToggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 1.2rem;
  color: inherit;
  z-index: 1001;
  transition: transform 0.3s ease;
  border-radius: 0.8rem;
  width: 4.8rem;
  height: 4.8rem;
  align-items: center;
  justify-content: center;
}

.mobileMenuToggle:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.05);
}

.mobileMenuToggle:active {
  transform: scale(0.95);
}

.mobileMenuToggle svg {
  width: 2.8rem;
  height: 2.8rem;
}

/* Mobile overlay */
.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease, visibility 0.4s ease;
}

.overlayOpen {
  opacity: 1;
  visibility: visible;
}

/* Mobile navigation */
.mobileNav {
  position: fixed;
  top: 0;
  right: -100%;
  height: 100vh;
  width: 32rem;
  max-width: 85vw;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: -0.4rem 0 2rem rgba(0, 0, 0, 0.15);
  transition: right 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobileNavOpen {
  right: 0;
}

.mobileNavContent {
  padding: 10rem 3rem 3rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Mobile-specific navigation styles */
.mobileNav .navContainer {
  flex-direction: column;
  gap: 0;
  height: 100%;
  align-items: stretch;
}

.mobileNav .linkList {
  flex-direction: column;
  gap: 0;
  margin-bottom: 4rem;
}

.mobileNav .linkList li {
  border-bottom: 0.1rem solid rgba(0, 0, 0, 0.1);
}

.mobileNav .linkList li:last-child {
  border-bottom: none;
}

.mobileNav .link,
.mobileNav .activeLink {
  display: block;
  padding: 1.6rem 0;
  font-size: 1.8rem;
  transition: padding-left 0.3s ease;
  border-left: 0.3rem solid transparent;
}

.mobileNav .link:hover,
.mobileNav .activeLink {
  padding-left: 1.2rem;
  border-left-color: currentColor;
}

.mobileNav .socialList {
  justify-content: center;
  gap: 2rem;
  margin-top: auto;
  padding-top: 3rem;
  border-top: 0.1rem solid rgba(0, 0, 0, 0.1);
}

.mobileNav .socialIcon {
  width: 2.8rem;
  height: 2.8rem;
  transition: transform 0.3s ease;
}

.mobileNav .socialLink:hover .socialIcon {
  transform: scale(1.2);
}

.mobileCta {
  margin-top: 3rem;
  text-align: center;
}

/* Responsive adjustments for medium screens (1200px and below) */
@media (max-width: 1700px) {
  .inner {
    gap: 1.5rem;
    padding: 1rem 1.5rem;
  }

  .logo img {
    height: 9rem;
    width: 9rem;
  }

  .nav {
    max-width: 65rem;
    margin: 0 1rem;
    padding: 0 0.8rem;
  }

  .navContainer {
    height: 6rem;
  }

  .linkList {
    gap: 1rem;
  }

  .socialList {
    gap: 1rem;
  }

  .link,
  .activeLink {
    font-size: 1.4rem;
    padding: 0.6rem 1rem;
  }

  .cta {
    font-size: 1.6rem;
  }

  .socialLink {
    padding: 0.8rem;
  }

  .socialIcon {
    width: 1.1rem;
    height: 1.1rem;
  }
}

@media (max-width: 1200px) {
  .logo img {
    height: 7rem;
    width: 7rem;
  }

  .nav {
    max-width: 55rem;
    margin: 0 1rem;
    padding: 0 0.8rem;
  }

  .navContainer {
    height: 4.5rem;
  }
  .link,
  .activeLink {
    font-size: 1.1rem;
    padding: 0.5rem 0.8rem;
  }
  .socialIcon {
    width: 0.8rem;
    height: 0.8rem;
  }
  .cta {
    font-size: 1.2rem;
  }
}

/* Additional adjustments for smaller medium screens */
@media (max-width: 1024px) {
  .inner {
    gap: 1rem;
  }

  .logo img {
    height: 6.5rem;
    width: 6.5rem;
  }

  .nav {
    max-width: 45rem;
    margin: 0 0.5rem;
  }

  .navContainer {
    height: 4rem;
  }

  .linkList {
    gap: 0.8rem;
  }

  .socialList {
    gap: 0.8rem;
  }

  .link,
  .activeLink {
    font-size: 1rem;
    padding: 0.2rem 0.6rem;
  }

  .cta {
    font-size: 1.1rem;
  }

  .socialLink {
    padding: 0.6rem;
  }

  .socialIcon {
    width: 0.8rem;
    height: 0.8rem;
  }
}

/* Mobile styles */
@media (max-width: 900px) {
  .inner {
    flex-direction: row;
    justify-content: space-between;
    min-height: 7rem;
    padding: 1rem 2rem;
  }

  .logo {
    max-width: 12rem;
    height: 7.5rem;
  }

  .logo img,
  .logo svg,
  .logo * {
    max-width: 100%;
    max-height: 7.5rem;
    width: auto;
    height: auto;
    object-fit: contain;
  }

  .mobileMenuToggle {
    display: flex;
  }

  .desktopNav,
  .desktopCta {
    display: none;
  }
  .nav {
    background-color: #fff;
  }
}

/* Tablet adjustments */
@media (max-width: 900px) and (min-width: 481px) {
  .logo {
    max-width: 14rem;
    height: 7.5rem;
  }

  .logo img,
  .logo svg,
  .logo * {
    max-height: 7.5rem;
  }

  .mobileMenuToggle {
    width: 5.5rem;
    height: 5.5rem;
  }

  .mobileMenuToggle svg {
    width: 5rem;
    height: 5rem;
  }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
  .inner {
    padding: 0.8rem 1.5rem;
    min-height: 6rem;
  }

  .logo {
    max-width: 10rem;
    height: 7.5rem;
  }

  .logo img,
  .logo svg,
  .logo * {
    max-height: 7.5rem;
  }

  .mobileMenuToggle {
    width: 5.5rem;
    height: 5.5rem;
  }

  .mobileMenuToggle svg {
    width: 5rem;
    height: 5rem;
  }
}

/* Desktop styles - hide mobile elements */
@media (min-width: 900px) {
  .mobileMenuToggle,
  .mobileOverlay,
  .mobileNav {
    display: none;
  }
}
