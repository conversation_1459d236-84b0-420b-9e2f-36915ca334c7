const MultiOptionsFilter = ({ items, activeIndex, onIndexChange }) => {
  return (
    <div className="flex ml-auto font-sans">
      <div className="relative bg-white/10 border-1 border-[#D6D6D6] rounded-[5rem] shadow-inner flex justify-between items-center">
        {/* Background Track */}
        <div className="flex py-1">
          {items.map((item, index) => (
            <button
              key={index}
              onClick={() => onIndexChange(index)}
              className="flex justify-center align-center z-10 px-3 py-1 rounded-full transition-all duration-300 hover:bg-white/20"
            >
              <span
                className={`
               text-md px-6 py-2 text-nowrap align-middle font-normal rounded-[10rem] transition-colors duration-300
                ${
                  index === activeIndex ? "text-white bg-primary" : "text-black"
                }
              `}
              >
                {item}
              </span>
            </button>
          ))}
        </div>

        {/* Sliding Indicator */}
        {/* <div
          className={`absolute py-1 w-[calc(100%/${totalItems})] h-full bg-primary  shadow-lg transition-transform duration-500 ease-out`}
          style={{
            transform: `translateX(calc(((100%/${totalItems})*${activeIndex})*${totalItems}))`,
            width: `calc(100%/${totalItems})`,
          }}
        /> */}
      </div>
    </div>
  );
};

export default MultiOptionsFilter;
