function Card({ title, imgSrc, subtitle, price, tags, isDark = false }) {
  price;
  return (
    <div className="md:text-center mb-8">
      <div className="relative w-full h-[60vh] min-h-[100px]">
        <img
          src={imgSrc}
          alt="Description"
          className="mx-auto object-cover h-full w-full"
        />
        {tags && (
          <div className="absolute top-6 left-6 right-6 flex space-x-2 flex-wrap gap-4 max-w-full">
            {tags.map((tag) => (
              <span
                key={tag}
                className="bg-white opacity-75 p-2 rounded-4xl backdrop-blur-xl py-1 px-6 text-lg font-outfit font-normal break-words"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
      <div className="text-center font-sans mt-6 md:text-left">
        <p className="text-3xl">{title}</p>
        <p className="text-gray-500 text-base">{subtitle}</p>
        {price && (
          <p className={`text-base ${isDark && "text-primary"}`}>{price}</p>
        )}
      </div>
    </div>
  );
}

export default Card;
