import PropTypes from "prop-types";
import { FiMapPin, FiDollarSign } from "react-icons/fi";

function Nearby({ property }) {
  if (!property?.location) return null;

  return (
    <>
      {/* Location & Nearby Section */}
      <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
        <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
          <FiMapPin className="text-primary" /> Location & Nearby
        </h3>
        <div className="mb-2 text-lg">
          <span className="font-semibold">Area:</span> {property.location?.area}
        </div>
        <div className="mb-2 text-lg">
          <span className="font-semibold">City:</span> {property.location?.city}
        </div>
        {property.location?.nearby && property.location.nearby.length > 0 && (
          <div className="mb-2">
            <div className="font-semibold mb-1">Nearby:</div>
            <div className="flex flex-wrap gap-2">
              {property.location.nearby.map((n, i) => (
                <span
                  key={i}
                  className="bg-primary/10 text-primary px-3 py-1 rounded-full text-base font-medium"
                >
                  {n}
                </span>
              ))}
            </div>
          </div>
        )}
      </section>

      {/* Payment Plan Section */}
      {property.payment_plan && (
        <section className="bg-white p-8 rounded-2xl shadow-md mb-12">
          <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
            <FiDollarSign className="text-primary" /> Payment Plan
          </h3>
          <div className="mb-2 text-lg font-semibold">
            {property.payment_plan.summary}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-4">
            <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
              <span className="text-primary font-bold text-lg mb-1">
                Down Payment
              </span>
              <span className="text-2xl font-extrabold">
                {property.payment_plan.details.down_payment_percent}%
              </span>
            </div>
            <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
              <span className="text-primary font-bold text-lg mb-1">
                During Construction
              </span>
              <span className="text-2xl font-extrabold">
                {property.payment_plan.details.during_construction_percent}%
              </span>
            </div>
            <div className="bg-primary/10 rounded-lg p-6 flex flex-col items-center">
              <span className="text-primary font-bold text-lg mb-1">
                Post-Handover
              </span>
              <span className="text-2xl font-extrabold">
                {property.payment_plan.details.post_handover_percent}%
              </span>
            </div>
          </div>
          {property.payment_plan.details.monthly_installments && (
            <div className="mt-4 text-base text-gray-700">
              Monthly Installments for{" "}
              <span className="font-semibold">
                {property.payment_plan.details.total_months}
              </span>{" "}
              months
            </div>
          )}
        </section>
      )}
    </>
  );
}

Nearby.propTypes = {
  property: PropTypes.shape({
    location: PropTypes.shape({
      area: PropTypes.string,
      city: PropTypes.string,
      nearby: PropTypes.arrayOf(PropTypes.string),
    }),
    payment_plan: PropTypes.shape({
      summary: PropTypes.string,
      details: PropTypes.shape({
        down_payment_percent: PropTypes.number,
        during_construction_percent: PropTypes.number,
        post_handover_percent: PropTypes.number,
        monthly_installments: PropTypes.bool,
        total_months: PropTypes.number,
      }),
    }),
  }).isRequired,
};

export default Nearby;
