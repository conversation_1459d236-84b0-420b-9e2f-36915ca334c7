import Subtitle from "../../../../components/Subtitle/index";
import firstImg from "../../../../assets/images/DA_1.jpg";
import { useState } from "react";

function DubaiAdvatage() {
  const [options, setOptions] = useState(
    ["Overview", "Urban Landscape", "Landmarks"].map((o, i) => ({
      name: o,
      isChoosen: !i,
    }))
  );

  function hanldeOptionChange(e) {
    setOptions((oldOptions) =>
      [...oldOptions].map((o) => ({
        ...o,
        isChoosen: e.target.value === o.name,
      }))
    );
  }

  return (
    <section className="py-20 bg-secondary text-secondary text-white">
      <div className="mx-auto px-6 md:px-30">
        <Subtitle text="The Dubai Advantage" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-5xl">
            Dubai: The Smart Choice for Investors
          </h2>
        </div>

        <div className="mt-15 flex flex-wrap justify-center gap-16 w-full">
          <img
            src={firstImg}
            className="object-cover h-[60vh] w-full max-w-xl"
            alt="Dubai"
          />
          <div className="m-auto text-center md:text-start md:max-w-1/2 flex flex-col items-start gap-8 w-full">
            <div className="flex mx-auto md:mx-0 gap-4 flex-wrap">
              {options.map((option) => (
                <button
                  key={option.name}
                  value={option.name}
                  onClick={hanldeOptionChange}
                  className={`py-2 px-6 bg-transparent text-nowrap text-xl border-1 rounded-4xl hover:bg-primary ${
                    option.isChoosen && "bg-primary !border-0"
                  }`}
                >
                  {option.name}
                </button>
              ))}
            </div>
            <p className="font-sans text-lg">
              Dubai is the most dynamic and prosperous city in the UAE, offering
              a unique blend of innovation, luxury, and opportunity. Its
              thriving economy and investor-friendly policies make it a global
              magnet for entrepreneurs and high-net-worth individuals. With no
              income tax, strong government support, and world-class
              infrastructure, Dubai stands out as a premier destinati on for
              real estate investment and elevated living.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default DubaiAdvatage;
