import Hero from "../features/destination/components/Hero";
import DubaiPropertyCollecion from "../features/destination/components/DubaiPropertyCollection/index.jsx";
import Discover from "../features/destination/components/Discover/index.jsx";
import DubaiAdvatage from "../features/destination/components/DubaiAdvantage/index.jsx";
import FooterWithEmail from "../layout/FooterWithEmail/index.jsx";
import { useEffect } from "react";

export default function Destinations() {
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  return (
    <div className="bg-[#EBE6E2]">
      <Hero />
      <DubaiPropertyCollecion />
      <Discover />
      <DubaiAdvatage />
      <FooterWithEmail />
    </div>
  );
}
