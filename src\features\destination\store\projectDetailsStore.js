// src/store/projectDetailsStore.js
import { create } from "zustand";
import axios from "axios";

const useProjectDetailsStore = create((set) => ({
  project: null,
  isLoading: false,
  error: null,

  fetchProjectById: async (id) => {
    set({ isLoading: true, error: null });
    try {
      const res = await axios.get(`http://localhost:3001/api/projects/${id}`);
      set({ project: res.data, isLoading: false });
    } catch (err) {
      console.error("Failed to fetch project:", err);
      set({ error: err.message, isLoading: false });
    }
  },
}));

export default useProjectDetailsStore;
