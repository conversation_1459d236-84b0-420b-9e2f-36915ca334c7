function Controller({ totalItems, activeItemIndex, onReviewChange }) {
  return (
    <div className="flex justify-between gap-8 items-center w-full">
      <button
        className={
          "font-sans text-lg disabled:cursor-not-allowed " +
          (activeItemIndex === 0 ? "text-gray" : "text-white")
        }
        onClick={() => onReviewChange(false)}
        disabled={activeItemIndex === 0}
      >
        Prev
      </button>

      {/* Main thing */}
      <div className="flex-1 h-0.5 py-0.5 relative bg-slate-200 shadow-inner">
        {/* Background Track */}
        <div>
          {Array.from({ length: totalItems }).map((_, index) => (
            <div
              key={index}
              className={
                "absolute -top-[.001rem] h-1 transition-all duration-300 " +
                (index <= activeItemIndex ? "bg-white" : "bg-[#415165]")
              }
              style={{
                left: `calc((100%/${totalItems})*${index})`,
                width: `calc(100%/${totalItems})`,
              }}
            />
          ))}
        </div>
      </div>

      <button
        className={
          "font-sans text-lg disabled:text-gray disabled:cursor-not-allowed " +
          (activeItemIndex >= totalItems - 1 ? "text-gray" : "text-white")
        }
        disabled={activeItemIndex >= totalItems - 1}
        onClick={() => onReviewChange(true)}
      >
        Next
      </button>
    </div>
  );
}

export default Controller;
