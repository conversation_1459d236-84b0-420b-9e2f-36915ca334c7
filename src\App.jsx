import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./layout/Header/Header";
// import './App.css'
import Home from "./pages/Home";
import Destinations from "./pages/Destinations";
import About from "./pages/About";
import PropertyDetail from "./features/destination/components/PropertyDetail";
import PropertyDetails from "./pages/PropertyDetails";

function Services() {
  return (
    <div className="page">
      <h1>Our Services</h1>
      <p>Discover our investment solutions</p>
    </div>
  );
}

function Contact() {
  return (
    <div className="page">
      <h1>Contact Us</h1>
      <p>Get in touch with our team</p>
    </div>
  );
}

function App() {
  return (
    <Router>
      <div className="App" id="top">
        <main>
          <Routes>
            <Route
              path="/"
              element={
                <>
                  <Header />
                  <Home />
                </>
              }
            />
            <Route
              path="/about"
              element={
                <>
                  <Header isDark={true} />
                  <About />
                </>
              }
            />
            <Route
              path="/services"
              element={
                <>
                  <Header />
                  <Services />
                </>
              }
            />
            <Route
              path="/contact"
              element={
                <>
                  <Header />
                  <Contact />
                </>
              }
            />
            <Route
              path="/destinations"
              element={
                <>
                  <Header />
                  <Destinations />
                </>
              }
            />
            <Route
              path="/projects/:id"
              element={
                <>
                  <Header />
                  <PropertyDetails />
                </>
              }
            />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
