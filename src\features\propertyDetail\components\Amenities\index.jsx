import PropTypes from "prop-types";
import { FiCheckCircle } from "react-icons/fi";

function Amenities({ property }) {
  if (!property?.amenities || property.amenities.length === 0) {
    return null;
  }

  return (
    <section className="bg-[#F7F6F3] p-8 rounded-2xl shadow-md mb-12">
      <h3 className="text-2xl font-bold mb-4 flex items-center gap-2 text-primary border-b-2 border-primary pb-1">
        <FiCheckCircle className="text-primary" /> Amenities
      </h3>
      <ul className="list-disc pl-6 space-y-2 text-lg">
        {property.amenities.map((amenity, index) => (
          <li key={index}>{amenity}</li>
        ))}
      </ul>
    </section>
  );
}

Amenities.propTypes = {
  property: PropTypes.shape({
    amenities: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};

export default Amenities;
