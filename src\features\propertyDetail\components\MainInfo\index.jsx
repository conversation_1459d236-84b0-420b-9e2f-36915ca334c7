import PropTypes from "prop-types";
import { FiHome, FiStar, FiInfo, FiCalendar } from "react-icons/fi";

function MainInfo({ property }) {
  if (!property) return null;

  return (
    <section className="bg-white rounded-2xl shadow-xl p-10 h-full flex flex-col justify-between">
      <div>
        <div className="flex items-center gap-3 mb-4">
          <FiHome className="text-3xl text-primary" />
          <h1 className="text-4xl font-extrabold text-gray-900">
            {property.title}
          </h1>
        </div>

        {property.subtitle && (
          <div className="flex items-center gap-3 mb-6">
            <FiStar className="text-2xl text-primary" />
            <div className="text-2xl text-primary font-semibold italic">
              {property.subtitle}
            </div>
          </div>
        )}

        {property.summary && (
          <div className="flex items-start gap-3 mb-6">
            <FiInfo className="text-xl text-primary mt-1" />
            <div className="text-lg text-gray-700 leading-relaxed">
              {property.summary}
            </div>
          </div>
        )}

        {property.desc && (
          <div className="flex items-start gap-3 mb-8">
            <FiInfo className="text-xl text-primary mt-1" />
            <p className="text-lg text-gray-700 leading-relaxed">
              {property.desc}
            </p>
          </div>
        )}
      </div>

      <div className="space-y-6">
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
            <FiHome className="text-xl" />
            {property.type}
          </div>
          <div className="flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-full text-lg font-semibold">
            <FiCalendar className="text-xl" />
            Handover: {property.handover}
          </div>
        </div>
      </div>
    </section>
  );
}

MainInfo.propTypes = {
  property: PropTypes.shape({
    title: PropTypes.string.isRequired,
    subtitle: PropTypes.string,
    summary: PropTypes.string,
    desc: PropTypes.string,
    type: PropTypes.string,
    handover: PropTypes.string,
  }).isRequired,
};

export default MainInfo;
