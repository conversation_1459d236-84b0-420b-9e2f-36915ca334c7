// store/destinationStore.js
import { create } from "zustand";

const useProjectStore = create((set) => ({
  projects: [],
  filters: {
    destinations: [],
    types: [],
    developer: null,
  },
  loading: false,
  error: null,

  setFilters: (filters) => set({ filters }),

  fetchProjects: async (filters = {}) => {
    set({
      loading: true,
      error: null,
      filters,
    });

    try {
      const filterConstructor = {};

      if (
        Array.isArray(filters.destinations) &&
        filters.destinations.length > 0 &&
        filters.destinations[0] !== "all"
      )
        filterConstructor.destinations = filters.destinations;
      if (
        Array.isArray(filters.types) &&
        filters.types.length > 0 &&
        filters.types[0] !== "all"
      )
        filterConstructor.types = filters.types;
      if (filters.developer && filters.developer !== "all") {
        filterConstructor.developer = filters.developer;
      }

      const response = await fetch("http://localhost:3001/api/projects/get", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(filterConstructor),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      const data = await response.json();

      set({
        projects: data.projects || [],
        loading: false,
      });
    } catch (error) {
      console.error("❌ API Error:", error);
      set({
        error: error.message,
        loading: false,
      });
    }
  },
}));

export default useProjectStore;
