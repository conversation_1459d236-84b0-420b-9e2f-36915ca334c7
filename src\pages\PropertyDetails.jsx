import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import axios from "axios";
import FooterWithEmail from "../layout/FooterWithEmail";

// Import refactored components
import Hero from "../features/propertyDetail/components/Hero";
import Gallery from "../features/propertyDetail/components/Gallery";
import MainInfo from "../features/propertyDetail/components/MainInfo";
import UnitTypes from "../features/propertyDetail/components/UnitTypes";
import PropertyFeatures from "../features/propertyDetail/components/PropertyFeatures";
import Nearby from "../features/propertyDetail/components/Nearby";
import Amenities from "../features/propertyDetail/components/Amenities";

function PropertyDetails() {
  const { id } = useParams();
  const [property, setProperty] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProperty = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `http://localhost:3001/api/projects/${id}`
        );
        setProperty(response.data);
      } catch (error) {
        console.error("Failed to fetch property:", error);
        setProperty(null);
      } finally {
        setLoading(false);
      }
    };
    fetchProperty();
  }, [id]);

  if (loading)
    return (
      <div className="min-h-screen bg-[#EBE6E2] flex items-center justify-center pt-55">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-8 border-primary border-t-transparent rounded-full animate-spin mb-4" />
          <div className="text-2xl font-semibold text-primary animate-pulse">
            Loading property details...
          </div>
        </div>
      </div>
    );

  if (!property)
    return (
      <div className="min-h-screen bg-[#EBE6E2] p-10 pt-55">
        <div className="max-w-5xl mx-auto">
          <Link
            to="/destinations"
            className="text-primary hover:underline mb-8 inline-block"
          >
            ← Back to properties
          </Link>
          <div className="text-3xl">Property not found</div>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-[#EBE6E2] pt-55">
      {/* Hero Section */}
      <Hero property={property} />

      <div className="mx-auto px-4 md:px-8 py-10 max-w-7xl">
        <Link
          to="/destinations"
          className="text-primary hover:underline mb-8 inline-block text-lg font-medium"
        >
          ← Back to properties
        </Link>

        {/* Gallery and Main Info Section */}
        {property.gallery && property.gallery.length > 0 && (
          <section className="mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Gallery Section - Left */}
              <div className="lg:order-1">
                <Gallery property={property} />
              </div>

              {/* Main Info Section - Right */}
              <div className="lg:order-2">
                <MainInfo property={property} />
              </div>
            </div>
          </section>
        )}

        {/* Unit Types Section */}
        <UnitTypes property={property} />

        {/* Property Features and Amenities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Property Features - Left */}
          <div className="lg:order-1">
            <PropertyFeatures property={property} />
          </div>

          {/* Amenities - Right */}
          <div className="lg:order-2">
            <Amenities property={property} />
          </div>
        </div>

        {/* Location, Nearby & Payment Plan Section */}
        <Nearby property={property} />
      </div>

      <FooterWithEmail />
    </div>
  );
}

export default PropertyDetails;
