import PropTypes from "prop-types";
import { FiLayers, FiDollarSign } from "react-icons/fi";

function UnitTypes({ property }) {
  if (!property?.unit_types || property.unit_types.length === 0) {
    return null;
  }

  const mainUnit = property.unit_types[0];
  const startingPrice = mainUnit
    ? `AED ${mainUnit.starting_price.toLocaleString()}`
    : "N/A";

  return (
    <section className="bg-white rounded-2xl shadow-xl p-8 mb-12">
      <div className="flex items-center gap-3 mb-6">
        <FiLayers className="text-3xl text-primary" />
        <h3 className="text-2xl font-bold text-primary">
          Unit Types & Pricing
        </h3>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-1/3">
          <div className="bg-gradient-to-br from-primary to-primary/80 text-white p-6 rounded-xl">
            <div className="flex items-center gap-2 mb-2">
              <FiDollarSign className="text-2xl" />
              <span className="text-lg font-semibold">Starting From</span>
            </div>
            <div className="text-3xl font-bold">{startingPrice}</div>
          </div>
        </div>

        <div className="flex-1 overflow-x-auto">
          <table className="min-w-full text-lg">
            <thead>
              <tr className="text-left border-b-2 border-primary">
                <th className="py-4 pr-8 text-xl font-bold text-primary">
                  Type
                </th>
                <th className="py-4 pr-8 text-xl font-bold text-primary">
                  Size (sqft)
                </th>
                <th className="py-4 pr-8 text-xl font-bold text-primary">
                  Starting Price
                </th>
              </tr>
            </thead>
            <tbody className="space-y-2">
              {property.unit_types.map((unit, index) => (
                <tr
                  key={unit._id}
                  className={`border-b last:border-b-0 hover:bg-gray-50 transition-colors ${
                    index % 2 === 0 ? "bg-gray-50/50" : ""
                  }`}
                >
                  <td className="py-4 pr-8 font-semibold text-lg">
                    {unit.type}
                  </td>
                  <td className="py-4 pr-8 text-lg">{unit.size_range_sqft}</td>
                  <td className="py-4 pr-8 text-lg font-semibold text-primary">
                    AED {unit.starting_price.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
}

UnitTypes.propTypes = {
  property: PropTypes.shape({
    unit_types: PropTypes.arrayOf(
      PropTypes.shape({
        _id: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        size_range_sqft: PropTypes.string.isRequired,
        starting_price: PropTypes.number.isRequired,
      })
    ),
  }).isRequired,
};

export default UnitTypes;
