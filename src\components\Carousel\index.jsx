import { useState } from "react";
import CarouselCard from "./components/CarouselCard";
import CarouselController from "./components/CarouselController";
import Card from "./../Card/Card";

const Carousel = ({ carouselData, itemsPerIndex = 3, isDark = false }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <div className="w-full mx-auto">
      {/* Carousel Items */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {carouselData
          .filter(
            (_, index) =>
              index >= itemsPerIndex * activeIndex &&
              index <= itemsPerIndex * activeIndex + (itemsPerIndex - 1)
          )
          .map((item) => (
            <Card
              key={item.id}
              imgSrc={item.image}
              title={item.title}
              price={item.price}
              subtitle={item.description}
              isDark={isDark}
            />
          ))}
      </div>

      {/* Custom Sliding Controller */}
      <CarouselController
        totalItems={carouselData.length}
        activeIndex={activeIndex}
        onIndexChange={setActiveIndex}
        itemsPerIndex={itemsPerIndex}
      />
    </div>
  );
};

export default Carousel;
