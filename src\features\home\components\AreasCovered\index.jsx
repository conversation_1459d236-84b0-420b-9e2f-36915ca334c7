import firstImg from "../../../../assets/images/1st.webp";
import secondImg from "../../../../assets/images/2nd.webp";
import Card from "../../../../components/Card/Card";
import Subtitle from "../../../../components/Subtitle";
import { Link } from "react-router-dom";

function AreasCovered() {
  return (
    <section className="py-20 bg-secondary text-white">
      <div className="mx-auto px-6 md:px-30">
        <Subtitle text="AREA THAT WE COVER" />
        <div className="grid md:grid-cols-12 gap-5">
          <h2 className="md:col-span-8 text-6xl leading-tight">
            If you're looking to invest in off plan <br></br> or secondary
            market,
            <span className="pl-4 text-primary italic font-bold">
              we can help you!
            </span>
          </h2>
          <div className="md:col-span-4 justify-self-end">
            <a className="text-lg cursor-pointer text-[#bea15d]">
              <span className="underline underline-offset-3">All cities</span>
              <span className="font-extrabold ml-3">→</span>
            </a>
          </div>
        </div>
        <div className="grid md:grid-cols-2 gap-8 mt-10">
          <Link to="destinations?destination=Dubai">
            <Card
              title="Dubai, UAE"
              subtitle="from 481.1k AED"
              imgSrc={firstImg}
            />
          </Link>
          <Link to="destinations?destination=Abu_Dhabi">
            <Card
              title="Abu Dhabi, UAE"
              subtitle="from 594K AED"
              imgSrc={secondImg}
            />
          </Link>
        </div>
      </div>
    </section>
  );
}

export default AreasCovered;
