import firstImg from "../../../assets/images/CR.webp";
import starIco from "../../../assets/icons/star.svg";
import { useState } from "react";
import Controller from "./Controller";

const customerReviewsInit = [
  {
    id: 1,
    comment:
      "Even though it's in the middle of the city, the natural feel is very calm and peaceful",
    author: "<PERSON>",
    company: "Shadows Inc Director",
    isActive: false,
    stars: 5,
  },
  {
    id: 2,
    comment:
      "The space is beautifully designed—minimalist but warm. It felt like a quiet retreat in the chaos.",
    author: "<PERSON>",
    company: "DesignWare Creative Lead",
    isActive: false,
    stars: 5,
  },
  {
    id: 3,
    comment:
      "Impressed by the attention to detail. From the lighting to the layout, everything felt intentional.",
    author: "<PERSON> Levin",
    company: "GridTech Operations Manager",
    isActive: true,
    stars: 4,
  },
  {
    id: 4,
    comment:
      "A perfect balance of urban energy and serene ambiance. I could've stayed for hours.",
    author: "<PERSON><PERSON>",
    company: "Satori Studios UX Researcher",
    isActive: false,
    stars: 5,
  },
  {
    id: 5,
    comment:
      "Clean, quiet, and well-maintained. Definitely a space that respects your time and presence.",
    author: "<PERSON>",
    company: "Novo Solutions Strategist",
    isActive: false,
    stars: 4,
  },
];

function CustomerReviews() {
  const [customerReviews, setCustomerReviews] = useState(customerReviewsInit);

  const activeCustomerReviewIndex = customerReviews.findIndex(
    (cr) => cr.isActive
  );

  function onReviewChange(isForeword) {
    setCustomerReviews((old) =>
      old.map((cr, i) => {
        if (cr.id === customerReviews[activeCustomerReviewIndex].id) {
          cr.isActive = false;
          customerReviews[
            isForeword ? ++i % customerReviews.length : --i
          ].isActive = true;
          return cr;
        }
        return cr;
      })
    );
  }

  return (
    <section className="pb-20 pt-14 bg-secondary text-white">
      <div className="mx-auto px-6 md:px-50">
        <div className="flex flex-wrap justify-between gap-8 md:gap-22">
          <div className="flex flex-col justify-around flex-1 gap-8">
            <div>
              <h2 className="text-5xl mb-4">
                What Our{" "}
                <span className="text-primary italic font-semibold">
                  Customers
                </span>{" "}
                Say
              </h2>
              <p className="font-sans text-base leading-tight">
                Following are some responses from our customers who are
                satisfied with the service from our home
              </p>
            </div>

            <Controller
              onReviewChange={onReviewChange}
              totalItems={customerReviews.length}
              activeItemIndex={activeCustomerReviewIndex}
            />
          </div>

          <div
            style={{
              background: `url(${firstImg})`,
              backgroundImage: `linear-gradient(
                  0deg,
                  #000 1.47%,
                  rgba(0, 0, 0, .1) 50.25%
                )
              , url(${firstImg})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
            className="z-10 py-20 min-h-[70vh] px-8 sm:px-20 min-w-80 flex-[2] flex flex-col justify-end gap-8 pb-20 font-sans"
          >
            <div className="flex gap-1">
              {Array.from({
                length: customerReviews[activeCustomerReviewIndex].stars,
              }).map((_, index) => (
                <img
                  key={index}
                  src={starIco}
                  alt="start"
                  className="block size-4"
                />
              ))}
            </div>
            <p className="text-3xl">
              “{customerReviews[activeCustomerReviewIndex]?.comment}”
            </p>
            <div>
              <p className="text-xl">
                {customerReviews[activeCustomerReviewIndex].author}
              </p>
              <p className="text-base text-gray">
                {customerReviews[activeCustomerReviewIndex].company}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default CustomerReviews;
