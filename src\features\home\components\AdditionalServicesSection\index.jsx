// import { useState } from 'react';
import firstImg from "../../../../assets/images/CSP_1.webp";
import secondImg from "../../../../assets/images/CSP_2.webp";
import thirdImg from "../../../../assets/images/CSP_3.webp";
import forthImg from "../../../../assets/images/CSP_2.webp";
import { FiArrowRight } from "react-icons/fi";
import { useState } from "react";

const services = [
  {
    id: 1,
    title: "Construction net Infrastructure Projects",
    image: firstImg,
    content: "Luxury development & models infrastructure enhancement.",
  },
  {
    id: 2,
    title: "Strategic et Branding Marketing",
    image: secondImg,
    content: "Luxury development & models infrastructure enhancement.",
  },
  {
    id: 3,
    title: "Restaurant Hospitality et Management",
    image: thirdImg,
    content: "Luxury development & models infrastructure enhancement.",
  },
  {
    id: 4,
    title: "Property Acquisition Asset et Management",
    image: forthImg,
    content: "Luxury development & models infrastructure enhancement.",
  },
];

export default function AdditionalServices() {
  const [activeCard, setActiveCard] = useState(null);

  const toggleCard = (id) => {
    if (window.innerWidth < 1024) {
      setActiveCard((prev) => (prev === id ? null : id));
    }
  };

  const handleMouseEnter = (id) => {
    if (window.innerWidth >= 1280) {
      setActiveCard(id);
    }
  };

  const handleMouseLeave = () => {
    if (window.innerWidth >= 1280) {
      setActiveCard(null);
    }
  };

  return (
    <section className="w-full bg-[#f5f1ee] py-20 px-4 sm:px-6 md:px-20">
      <div className="max-w-[1926px] mx-auto ">
        <h2 className="font-[500] text-[28px] leading-[40px] tracking-[-0.025em] sm:text-[36px] sm:leading-[48px] xl:text-[44px] xl:leading-[60px] 2xl:text-[55px] 2xl:leading-[68px] text-center">
          We are not just a real estate brokerage company
          <br />
          we also offer a range of{" "}
          <span className="italic text-[#BEA15D] font-semibold lining-nums proportional-nums">
            additional services !
          </span>
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 mt-16 xl:mt-24 divide-x divide-gray-300">
          {services.map((service) => {
            const isActive = activeCard === service.id;
            return (
              <div
                key={service.id}
                className={`relative h-[300px] sm:h-[320px] xl:h-[350px] overflow-hidden transition-all duration-700 ease-in-out group cursor-pointer`}
                onClick={() => toggleCard(service.id)}
                onMouseEnter={() => handleMouseEnter(service.id)}
                onMouseLeave={handleMouseLeave}
              >
                {/* Top Content */}
                <div
                  className={`absolute inset-0 flex flex-col text-left justify-center  px-4 transition-all duration-700 ease-in-out
                    ${isActive ? "h-1/2 top-0 bg-[#BEA15D]" : "h-full"}
                  `}
                >
                  <div
                    className={`transition-all duration-700 ease-in-out flex flex-col justify-center
                      ${isActive ? "-translate-y-2" : "translate-y-0"}`}
                  >
                    <p
                      className={`font-[Outfit] text-[16px] sm:text-[18px] xl:text-[20px] font-normal leading-[24px] xl:leading-[30px] tracking-[-0.04em] mb-2 transition-all duration-700 ease-in-out
                        ${isActive ? "text-white scale-90" : "text-[#1e1e1e]"}`}
                    >
                      0{service.id}
                    </p>
                    <h3
                      className={`font-[Outfit] text-[19px] font-normal leading-[19px] tracking-[-0.04em] whitespace-pre-line transition-all duration-700 ease-in-out 2xl:text-4xl xl:text-4xl
    ${isActive ? "text-white scale-90" : "text-[#1e1e1e]"}`}
                    >
                      {service.title}
                    </h3>
                    <p
                      className={`mt-2 ml-5 font-[Outfit] text-[19px] font-normal leading-[19px] tracking-[-0.04em] w-full max-w-[90%]  transition-opacity duration-700 ease-in-out
    ${isActive ? "opacity-100 text-white" : "opacity-0  text-white "}`}
                    >
                      {service.content}
                    </p>
                  </div>

                  <div
                    className={`mt-4 transition-opacity duration-500 ease-in-out
                      ${isActive ? "opacity-0" : "opacity-100 text-[#BEA15D]"}`}
                  >
                    <FiArrowRight
                      style={{ width: "26.62px", height: "26.61px" }}
                    />
                  </div>
                </div>

                {/* Bottom Image */}
                <div
                  className={`absolute left-0 w-full h-1/2 bg-cover bg-center transition-all duration-700 ease-in-out ${
                    isActive ? "bottom-0" : "bottom-[-50%]"
                  }`}
                  style={{
                    backgroundImage: `url(${service.image})`,
                  }}
                ></div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
