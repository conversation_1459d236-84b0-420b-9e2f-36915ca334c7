import { useEffect } from "react";
import { Link } from "react-router-dom";
import {
  FiMapPin,
  FiHome,
  FiDollarSign,
  FiArrowRight,
  FiStar,
  FiSearch,
  FiFilter,
} from "react-icons/fi";
import useProjectStore from "../../store/destinationStore";

const ProjectsGrid = () => {
  const {
    projects,
    loading, // use loading from zustand
    fetchProjects,
    filters,
  } = useProjectStore();

  useEffect(() => {
    if (filters?.destinations || filters?.types) {
      fetchProjects(filters);
    }
  }, [filters]);

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center h-80">
        <div className="w-16 h-16 border-8 border-primary border-t-transparent rounded-full animate-spin mb-4" />
        <span className="text-lg font-semibold text-primary">
          Loading projects...
        </span>
      </div>
    );
  }

  // Check if filters are applied
  const hasActiveFilters =
    filters &&
    ((filters.destinations &&
      filters.destinations.length > 0 &&
      filters.destinations[0] !== "all") ||
      (filters.types &&
        filters.types.length > 0 &&
        filters.types[0] !== "all") ||
      (filters.developer && filters.developer !== "all"));

  // Empty state when no projects found with filters
  if (hasActiveFilters && projects.length === 0) {
    return (
      <section className="px-4 sm:px-8 lg:px-20 py-10 bg-gray-50">
        <h2 className="text-4xl font-extrabold mb-10 text-gray-800 text-center tracking-tight">
          Available{" "}
          <span className="text-primary italic underline decoration-2">
            Projects
          </span>
        </h2>

        <div className="flex flex-col items-center justify-center py-20">
          <div className="bg-white rounded-3xl shadow-xl p-12 max-w-2xl text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-primary/10 p-6 rounded-full">
                <FiSearch className="text-6xl text-primary" />
              </div>
            </div>

            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              No Projects Found
            </h3>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              We couldn't find any projects matching your current filters. Try
              adjusting your search criteria or browse all available projects.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  const resetFilters = {
                    destinations: ["all"],
                    types: ["all"],
                    developer: "all",
                  };
                  fetchProjects(resetFilters);
                }}
                className="flex items-center justify-center gap-3 bg-primary text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary/90 transition-colors"
              >
                <FiFilter className="text-xl" />
                Clear All Filters
              </button>

              <Link
                to="/destinations"
                className="flex items-center justify-center gap-3 border-2 border-primary text-primary px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary hover:text-white transition-colors"
              >
                <FiArrowRight className="text-xl" />
                Browse All Projects
              </Link>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="px-4 sm:px-8 lg:px-20 py-10 bg-gray-50">
      <h2 className="text-4xl font-extrabold mb-10 text-gray-800 text-center tracking-tight">
        Available{" "}
        <span className="text-primary  decoration-2">
          Projects
        </span>
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
        {projects.map((project) => (
          <div
            key={project._id}
            className="bg-white shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden flex flex-col min-h-[420px] group border border-gray-100 hover:-translate-y-2"
          >
            <div className="relative h-72 w-full">
              <img
                src={project.mainImage}
                alt={project.title}
                className="w-full h-full object-cover object-center  transition-transform duration-500 hover:object-scale-110"
              />
              <div className="absolute top-4 left-4 flex gap-2 z-10">
                <div className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded-full shadow-lg">
                  <FiHome className="text-lg" />
                  <span className="text-lg font-bold">{project.type}</span>
                </div>
                <div className="flex items-center gap-2 bg-white/90 text-primary px-4 py-2 rounded-full shadow-lg">
                  <FiDollarSign className="text-lg" />
                  <span className="text-lg font-bold">
                    AED {project.starting_price.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
            <div className="p-6 flex-1 flex flex-col justify-between">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-2">
                  <FiStar className="text-2xl text-primary" />
                  <h3 className="text-2.5xl sm:text-3xl font-extrabold text-gray-900 leading-tight tracking-tight">
                    {project.title}
                  </h3>
                </div>

                <div className="flex items-center gap-3 mb-3">
                  <FiMapPin className="text-xl text-primary" />
                  <span className="text-gray-700 text-xl font-medium">
                    {project.location}
                  </span>
                </div>

                <p className="text-gray-700 text-lg sm:text-xl leading-relaxed">
                  {project.desc}
                </p>
              </div>

              <Link
                to={`/projects/${project._id}`}
                className="w-full mt-6 inline-flex items-center justify-center gap-3 text-xl font-bold text-white bg-primary hover:bg-secondary transition-all duration-300 px-6 py-4 rounded-xl shadow-lg tracking-wide group-hover:shadow-xl"
              >
                Read More
                <FiArrowRight className="text-xl group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ProjectsGrid;
