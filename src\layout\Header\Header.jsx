import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import styles from "./Header.module.css";
import Logo from "./Logo";
import NavLinks from "./NavLinks";
import CTALink from "./CTALink";
import { HiMenu, HiX } from "react-icons/hi";

export default function Header({ isDark = false }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  const istHomePage = location.pathname === "/";

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // useEffect(() => {
  //   const handleScroll = () => {
  //     const scrollPosition = window.scrollY;
  //     const viewportHeight = window.innerHeight;

  //    setIsScrolled(scrollPosition > viewportHeight * 0.5);
  //   };

  //   window.addEventListener('scroll', handleScroll);

  //   handleScroll();

  //   return () => window.removeEventListener('scroll', handleScroll);
  // }, []);

  // Use the appropriate CSS classes based on the current page
  // const headerClass = istHomePage ?  styles.header_home : styles.header;
  const navClass = istHomePage ? styles.nav_home : styles.nav;

  return (
    <header
      className={`${styles.header} ${isScrolled ? styles.headerScrolled : ""} ${
        isDark ? "text-white" : "text-secondary"
      }`}
    >
      <div className={styles.inner}>
        {/* Logo on the left */}
        <div className={styles.logo}>
          <Logo />
        </div>

        {/* Navigation in the center - Desktop */}
        <nav className={`${navClass} ${styles.desktopNav}`}>
          <NavLinks />
        </nav>

        {/* CTA Button on the right - Desktop */}
        <div className={`${styles.cta} ${styles.desktopCta}`}>
          <CTALink />
        </div>

        {/* Mobile Menu Toggle Button */}
        <button
          className={styles.mobileMenuToggle}
          onClick={toggleMenu}
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        >
          {isMenuOpen ? <HiX /> : <HiMenu />}
        </button>
      </div>

      {/* Mobile Navigation Overlay */}
      <div
        className={`${styles.mobileOverlay} ${
          isMenuOpen ? styles.overlayOpen : ""
        }`}
        onClick={closeMenu}
      ></div>

      {/* Mobile Navigation */}
      <nav
        className={`${navClass} ${styles.mobileNav} ${
          isMenuOpen ? styles.mobileNavOpen : ""
        }`}
      >
        <div className={styles.mobileNavContent}>
          <NavLinks onLinkClick={closeMenu} />
          <div className={styles.mobileCta}>
            <CTALink onLinkClick={closeMenu} />
          </div>
        </div>
      </nav>
    </header>
  );
}
